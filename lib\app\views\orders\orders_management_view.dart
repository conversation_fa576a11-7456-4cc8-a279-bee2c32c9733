import 'package:flutter/material.dart';

class OrdersManagementView extends StatelessWidget {
  const OrdersManagementView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                'Orders Management',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: () {},
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh'),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Status Filter Tabs
          Row(
            children: [
              _buildStatusTab('All', true),
              _buildStatusTab('Pending', false),
              _buildStatusTab('Preparing', false),
              _buildStatusTab('Ready', false),
              _buildStatusTab('Served', false),
            ],
          ),
          const SizedBox(height: 20),
          
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: ListView.builder(
                  itemCount: 8,
                  itemBuilder: (context, index) {
                    return _buildOrderCard(
                      orderNumber: '#${1001 + index}',
                      table: 'Table ${index + 1}',
                      items: '${2 + index % 3} items',
                      amount: '₹${(450 + index * 100)}',
                      status: index % 4 == 0 ? 'Pending' :
                             index % 4 == 1 ? 'Preparing' :
                             index % 4 == 2 ? 'Ready' : 'Served',
                      time: '${10 + index} min ago',
                      customerName: 'Customer ${index + 1}',
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusTab(String status, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(right: 10),
      child: FilterChip(
        label: Text(status),
        selected: isSelected,
        onSelected: (selected) {},
        selectedColor: Colors.blue.shade100,
        checkmarkColor: Colors.blue.shade700,
      ),
    );
  }

  Widget _buildOrderCard({
    required String orderNumber,
    required String table,
    required String items,
    required String amount,
    required String status,
    required String time,
    required String customerName,
  }) {
    Color statusColor = status == 'Pending' ? Colors.orange :
                       status == 'Preparing' ? Colors.blue :
                       status == 'Ready' ? Colors.purple : Colors.green;

    return Card(
      margin: const EdgeInsets.only(bottom: 15),
      child: Padding(
        padding: const EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      orderNumber,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '$table • $customerName',
                      style: TextStyle(color: Colors.grey.shade600),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      amount,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    Text(
                      time,
                      style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  items,
                  style: TextStyle(color: Colors.grey.shade600),
                ),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: statusColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        status,
                        style: TextStyle(
                          color: statusColor,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 10),
                    if (status != 'Served')
                      ElevatedButton(
                        onPressed: () {},
                        style: ElevatedButton.styleFrom(
                          backgroundColor: statusColor,
                          minimumSize: const Size(80, 30),
                        ),
                        child: Text(
                          status == 'Pending' ? 'Accept' :
                          status == 'Preparing' ? 'Ready' : 'Serve',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
