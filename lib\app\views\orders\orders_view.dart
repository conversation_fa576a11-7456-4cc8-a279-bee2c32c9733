import 'package:flutter/material.dart';

class OrdersView extends StatelessWidget {
  const OrdersView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Orders')),
      body: const Center(
        child: Text('Orders Management - Coming Soon'),
      ),
    );
  }
}

class OrderDetailsView extends StatelessWidget {
  final String orderId;
  
  const OrderDetailsView({super.key, required this.orderId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Order Details')),
      body: Center(
        child: Text('Order Details: $orderId - Coming Soon'),
      ),
    );
  }
}
