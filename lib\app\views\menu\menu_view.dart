import 'package:flutter/material.dart';

class MenuView extends StatelessWidget {
  const MenuView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Menu Management')),
      body: const Center(
        child: Text('Menu Management - Coming Soon'),
      ),
    );
  }
}

class MenuAddView extends StatelessWidget {
  const MenuAddView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Add Menu Item')),
      body: const Center(
        child: Text('Add Menu Item - Coming Soon'),
      ),
    );
  }
}

class MenuEditView extends StatelessWidget {
  final String menuItemId;
  
  const MenuEditView({super.key, required this.menuItemId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Edit Menu Item')),
      body: Center(
        child: Text('Edit Menu Item: $menuItemId - Coming Soon'),
      ),
    );
  }
}
