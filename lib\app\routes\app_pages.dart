import 'package:go_router/go_router.dart';
import 'package:get/get.dart';
import '../controllers/auth_controller.dart';
import '../views/splash_view.dart';
import '../views/auth/login_view.dart';
import '../views/dashboard/dashboard_view.dart';
import '../views/menu/menu_view.dart';


import '../views/orders/orders_view.dart';

import '../views/tables/tables_view.dart';
import '../views/reports/reports_view.dart';
import '../views/settings/settings_view.dart';
import '../views/qr/qr_generator_view.dart';
import 'app_routes.dart';

class AppPages {
  static final GoRouter router = GoRouter(
    initialLocation: AppRoutes.splash,
    redirect: (context, state) {
      final authController = Get.find<AuthController>();
      final isLoggedIn = authController.isLoggedIn;
      final isLoggingIn = state.matchedLocation == AppRoutes.login;

      // If not logged in and not on login page, redirect to login
      if (!isLoggedIn && !isLoggingIn) {
        return AppRoutes.login;
      }

      // If logged in and on login page, redirect to dashboard
      if (isLoggedIn && isLoggingIn) {
        return AppRoutes.dashboard;
      }

      return null; // No redirect needed
    },
    routes: [
      GoRoute(
        path: AppRoutes.splash,
        builder: (context, state) => const SplashView(),
      ),
      GoRoute(
        path: AppRoutes.login,
        builder: (context, state) => const LoginView(),
      ),
      GoRoute(
        path: AppRoutes.dashboard,
        builder: (context, state) => const DashboardView(),
      ),
      GoRoute(
        path: AppRoutes.menu,
        builder: (context, state) => const MenuView(),
      ),
      GoRoute(
        path: AppRoutes.orders,
        builder: (context, state) => const OrdersView(),
      ),
      GoRoute(
        path: AppRoutes.tables,
        builder: (context, state) => const TablesView(),
      ),
      GoRoute(
        path: AppRoutes.reports,
        builder: (context, state) => const ReportsView(),
      ),
      GoRoute(
        path: AppRoutes.settings,
        builder: (context, state) => const SettingsView(),
      ),
      GoRoute(
        path: AppRoutes.qrGenerator,
        builder: (context, state) => const QrGeneratorView(),
      ),
    ],
  );
}
