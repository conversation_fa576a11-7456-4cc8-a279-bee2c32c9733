import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/navigation_controller.dart';

class AdminLayout extends StatelessWidget {
  final Widget child;
  
  const AdminLayout({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    final AuthController authController = Get.find<AuthController>();
    final NavigationController navController = Get.put(NavigationController());

    return Scaffold(
      body: Row(
        children: [
          // Sidebar Navigation
          Container(
            width: 250,
            color: Theme.of(context).colorScheme.surface,
            child: Column(
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  child: Column(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: Colors.white,
                        child: Icon(
                          Icons.restaurant,
                          size: 30,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        'Baroda Kayaks Cafe',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Admin Panel',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Navigation Menu
                Expanded(
                  child: ListView(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    children: [
                      _buildNavItem(
                        icon: Icons.dashboard,
                        title: 'Dashboard',
                        index: 0,
                        navController: navController,
                      ),
                      _buildNavItem(
                        icon: Icons.restaurant_menu,
                        title: 'Menu Management',
                        index: 1,
                        navController: navController,
                      ),
                      _buildNavItem(
                        icon: Icons.receipt_long,
                        title: 'Orders',
                        index: 2,
                        navController: navController,
                      ),
                      _buildNavItem(
                        icon: Icons.table_restaurant,
                        title: 'Tables',
                        index: 3,
                        navController: navController,
                      ),
                      _buildNavItem(
                        icon: Icons.qr_code,
                        title: 'QR Codes',
                        index: 4,
                        navController: navController,
                      ),
                      _buildNavItem(
                        icon: Icons.analytics,
                        title: 'Reports',
                        index: 5,
                        navController: navController,
                      ),
                      _buildNavItem(
                        icon: Icons.settings,
                        title: 'Settings',
                        index: 6,
                        navController: navController,
                      ),
                    ],
                  ),
                ),
                
                // User Info & Logout
                Container(
                  padding: const EdgeInsets.all(15),
                  decoration: BoxDecoration(
                    border: Border(
                      top: BorderSide(
                        color: Colors.grey.shade300,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Column(
                    children: [
                      Obx(() => ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          child: Text(
                            authController.user?.name.substring(0, 1).toUpperCase() ?? 'U',
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                        title: Text(
                          authController.user?.name ?? 'User',
                          style: const TextStyle(fontSize: 14),
                        ),
                        subtitle: Text(
                          authController.user?.role.toUpperCase() ?? 'USER',
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        contentPadding: EdgeInsets.zero,
                      )),
                      const SizedBox(height: 10),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () => authController.signOut(),
                          icon: const Icon(Icons.logout, size: 18),
                          label: const Text('Logout'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red.shade50,
                            foregroundColor: Colors.red.shade700,
                            elevation: 0,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Main Content Area
          Expanded(
            child: Column(
              children: [
                // Top App Bar
                Container(
                  height: 60,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.shade200,
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Obx(() => Text(
                        _getPageTitle(navController.selectedIndex.value),
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      )),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.notifications),
                        onPressed: () {},
                      ),
                      IconButton(
                        icon: const Icon(Icons.refresh),
                        onPressed: () {},
                      ),
                    ],
                  ),
                ),
                
                // Page Content
                Expanded(
                  child: Container(
                    color: Colors.grey.shade50,
                    child: child,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String title,
    required int index,
    required NavigationController navController,
  }) {
    return Obx(() => Container(
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
      child: ListTile(
        leading: Icon(
          icon,
          color: navController.selectedIndex.value == index
              ? Theme.of(Get.context!).colorScheme.primary
              : Colors.grey.shade600,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: navController.selectedIndex.value == index
                ? Theme.of(Get.context!).colorScheme.primary
                : Colors.grey.shade700,
            fontWeight: navController.selectedIndex.value == index
                ? FontWeight.bold
                : FontWeight.normal,
          ),
        ),
        selected: navController.selectedIndex.value == index,
        selectedTileColor: Theme.of(Get.context!).colorScheme.primary.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        onTap: () => navController.changePage(index),
      ),
    ));
  }

  String _getPageTitle(int index) {
    switch (index) {
      case 0:
        return 'Dashboard';
      case 1:
        return 'Menu Management';
      case 2:
        return 'Orders';
      case 3:
        return 'Tables';
      case 4:
        return 'QR Codes';
      case 5:
        return 'Reports';
      case 6:
        return 'Settings';
      default:
        return 'Dashboard';
    }
  }
}
