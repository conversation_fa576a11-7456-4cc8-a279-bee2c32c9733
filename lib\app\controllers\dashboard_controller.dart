import 'package:get/get.dart';

class DashboardController extends GetxController {
  final RxInt todayOrders = 0.obs;
  final RxDouble todayRevenue = 0.0.obs;
  final RxInt activeTables = 0.obs;
  final RxInt totalTables = 20.obs;
  final RxInt menuItems = 0.obs;

  @override
  void onInit() {
    super.onInit();
    loadDashboardData();
  }

  void loadDashboardData() {
    // Simulate loading data
    todayOrders.value = 24;
    todayRevenue.value = 8450.0;
    activeTables.value = 12;
    menuItems.value = 45;
  }

  void refreshData() {
    loadDashboardData();
  }
}
