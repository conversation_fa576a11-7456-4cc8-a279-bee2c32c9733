import 'package:cloud_firestore/cloud_firestore.dart';

enum TableStatus { available, occupied, reserved, cleaning }

class TableModel {
  final String id;
  final String name;
  final int capacity;
  final TableStatus status;
  final String? currentOrderId;
  final String qrCode;
  final String location; // e.g., "Ground Floor", "Terrace"
  final DateTime createdAt;
  final DateTime updatedAt;

  TableModel({
    required this.id,
    required this.name,
    required this.capacity,
    this.status = TableStatus.available,
    this.currentOrderId,
    required this.qrCode,
    this.location = '',
    required this.createdAt,
    required this.updatedAt,
  });

  factory TableModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return TableModel(
      id: doc.id,
      name: data['name'] ?? '',
      capacity: data['capacity'] ?? 2,
      status: TableStatus.values.firstWhere(
        (e) => e.toString() == 'TableStatus.${data['status']}',
        orElse: () => TableStatus.available,
      ),
      currentOrderId: data['currentOrderId'],
      qrCode: data['qrCode'] ?? '',
      location: data['location'] ?? '',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'capacity': capacity,
      'status': status.toString().split('.').last,
      'currentOrderId': currentOrderId,
      'qrCode': qrCode,
      'location': location,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  TableModel copyWith({
    String? id,
    String? name,
    int? capacity,
    TableStatus? status,
    String? currentOrderId,
    String? qrCode,
    String? location,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TableModel(
      id: id ?? this.id,
      name: name ?? this.name,
      capacity: capacity ?? this.capacity,
      status: status ?? this.status,
      currentOrderId: currentOrderId ?? this.currentOrderId,
      qrCode: qrCode ?? this.qrCode,
      location: location ?? this.location,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get statusDisplayName {
    switch (status) {
      case TableStatus.available:
        return 'Available';
      case TableStatus.occupied:
        return 'Occupied';
      case TableStatus.reserved:
        return 'Reserved';
      case TableStatus.cleaning:
        return 'Cleaning';
    }
  }
}
