import 'package:flutter/material.dart';

class TablesManagementView extends StatelessWidget {
  const TablesManagementView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Table Management',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              ElevatedButton.icon(
                onPressed: () {},
                icon: const Icon(Icons.add),
                label: const Text('Add Table'),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Table Status Summary
          Row(
            children: [
              _buildStatusSummary('Available', 8, Colors.green),
              const SizedBox(width: 15),
              _buildStatusSummary('Occupied', 12, Colors.red),
              const SizedBox(width: 15),
              _buildStatusSummary('Reserved', 2, Colors.orange),
              const SizedBox(width: 15),
              _buildStatusSummary('Cleaning', 1, Colors.blue),
            ],
          ),
          const SizedBox(height: 30),
          
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Table Layout',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 20),
                    Expanded(
                      child: GridView.builder(
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 5,
                          crossAxisSpacing: 15,
                          mainAxisSpacing: 15,
                          childAspectRatio: 1.2,
                        ),
                        itemCount: 20,
                        itemBuilder: (context, index) {
                          String status = index < 8 ? 'Available' :
                                        index < 20 ? 'Occupied' :
                                        index < 22 ? 'Reserved' : 'Cleaning';
                          return _buildTableCard(
                            tableNumber: index + 1,
                            status: status,
                            capacity: 2 + (index % 4),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusSummary(String status, int count, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(15),
          child: Column(
            children: [
              Icon(Icons.table_restaurant, color: color, size: 30),
              const SizedBox(height: 8),
              Text(
                count.toString(),
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                status,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTableCard({
    required int tableNumber,
    required String status,
    required int capacity,
  }) {
    Color statusColor = status == 'Available' ? Colors.green :
                       status == 'Occupied' ? Colors.red :
                       status == 'Reserved' ? Colors.orange : Colors.blue;

    return Card(
      color: statusColor.withOpacity(0.1),
      child: InkWell(
        onTap: () {
          // Show table details
        },
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.table_restaurant,
                color: statusColor,
                size: 30,
              ),
              const SizedBox(height: 5),
              Text(
                'Table $tableNumber',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              Text(
                '$capacity seats',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 10,
                ),
              ),
              const SizedBox(height: 5),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: statusColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  status,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 8,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
