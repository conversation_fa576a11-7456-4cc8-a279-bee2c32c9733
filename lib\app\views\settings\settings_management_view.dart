import 'package:flutter/material.dart';

class SettingsManagementView extends StatelessWidget {
  const SettingsManagementView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Settings',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // General Settings
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'General Settings',
                            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 20),
                          
                          _buildSettingItem(
                            'Restaurant Name',
                            'Baroda Kayaks Cafe',
                            Icons.restaurant,
                            () {},
                          ),
                          _buildSettingItem(
                            'Contact Number',
                            '+91 98765 43210',
                            Icons.phone,
                            () {},
                          ),
                          _buildSettingItem(
                            'Email Address',
                            '<EMAIL>',
                            Icons.email,
                            () {},
                          ),
                          _buildSettingItem(
                            'Address',
                            '123 Cafe Street, Baroda',
                            Icons.location_on,
                            () {},
                          ),
                          
                          const SizedBox(height: 30),
                          
                          const Text(
                            'Operational Settings',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 15),
                          
                          _buildSwitchSetting('Accept Online Orders', true),
                          _buildSwitchSetting('Enable Notifications', true),
                          _buildSwitchSetting('Auto-print Orders', false),
                          _buildSwitchSetting('Table Reservations', true),
                          
                          const SizedBox(height: 30),
                          
                          const Text(
                            'Tax & Pricing',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 15),
                          
                          _buildSettingItem(
                            'GST Rate',
                            '18%',
                            Icons.percent,
                            () {},
                          ),
                          _buildSettingItem(
                            'Service Charge',
                            '10%',
                            Icons.attach_money,
                            () {},
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 20),
                
                // User Management & Security
                Expanded(
                  child: Column(
                    children: [
                      // User Management
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text(
                                    'User Management',
                                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                                  ),
                                  ElevatedButton.icon(
                                    onPressed: () {},
                                    icon: const Icon(Icons.person_add),
                                    label: const Text('Add User'),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 20),
                              
                              _buildUserItem('Admin User', '<EMAIL>', 'Admin'),
                              _buildUserItem('Manager', '<EMAIL>', 'Manager'),
                              _buildUserItem('Staff 1', '<EMAIL>', 'Staff'),
                            ],
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 20),
                      
                      // Backup & Security
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Backup & Security',
                                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 20),
                              
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton.icon(
                                  onPressed: () {},
                                  icon: const Icon(Icons.backup),
                                  label: const Text('Backup Data'),
                                ),
                              ),
                              const SizedBox(height: 10),
                              SizedBox(
                                width: double.infinity,
                                child: OutlinedButton.icon(
                                  onPressed: () {},
                                  icon: const Icon(Icons.restore),
                                  label: const Text('Restore Data'),
                                ),
                              ),
                              const SizedBox(height: 20),
                              
                              _buildSwitchSetting('Two-Factor Authentication', false),
                              _buildSwitchSetting('Login Notifications', true),
                              
                              const SizedBox(height: 20),
                              
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton.icon(
                                  onPressed: () {},
                                  icon: const Icon(Icons.lock),
                                  label: const Text('Change Password'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem(String title, String value, IconData icon, VoidCallback onTap) {
    return Container(
      margin: const EdgeInsets.only(bottom: 15),
      child: ListTile(
        leading: Icon(icon, color: Colors.blue.shade700),
        title: Text(title),
        subtitle: Text(value),
        trailing: const Icon(Icons.edit, size: 20),
        onTap: onTap,
        contentPadding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildSwitchSetting(String title, bool value) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title),
          Switch(
            value: value,
            onChanged: (newValue) {},
          ),
        ],
      ),
    );
  }

  Widget _buildUserItem(String name, String email, String role) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade200),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: Colors.blue.shade100,
            child: Text(name.substring(0, 1)),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(name, style: const TextStyle(fontWeight: FontWeight.bold)),
                Text(email, style: TextStyle(color: Colors.grey.shade600, fontSize: 12)),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              role,
              style: TextStyle(
                color: Colors.blue.shade700,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'edit', child: Text('Edit')),
              const PopupMenuItem(value: 'delete', child: Text('Delete')),
            ],
            onSelected: (value) {},
          ),
        ],
      ),
    );
  }
}
