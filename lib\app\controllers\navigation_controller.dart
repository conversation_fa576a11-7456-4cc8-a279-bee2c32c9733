import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../views/dashboard/dashboard_content.dart';
import '../views/menu/menu_management_view.dart';
import '../views/orders/orders_management_view.dart';
import '../views/tables/tables_management_view.dart';
import '../views/qr/qr_management_view.dart';
import '../views/reports/reports_management_view.dart';
import '../views/settings/settings_management_view.dart';

class NavigationController extends GetxController {
  final RxInt selectedIndex = 0.obs;

  final List<Widget> pages = [
    const DashboardContent(),
    const MenuManagementView(),
    const OrdersManagementView(),
    const TablesManagementView(),
    const QrManagementView(),
    const ReportsManagementView(),
    const SettingsManagementView(),
  ];

  void changePage(int index) {
    selectedIndex.value = index;
  }

  Widget get currentPage => pages[selectedIndex.value];
}
